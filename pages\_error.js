import { siteConfig } from '@/lib/config'
import { useGlobal } from '@/lib/global'
import { DynamicLayout } from '@/themes/theme'

function Error({ statusCode, hasGetInitialPropsRun, err }) {
  const { locale } = useGlobal()
  
  // 记录错误信息
  if (err && !hasGetInitialPropsRun) {
    console.error('页面错误:', err)
  }

  const errorMessage = statusCode
    ? locale.COMMON.ERROR_WITH_CODE?.replace('{code}', statusCode) || `服务器发生 ${statusCode} 错误`
    : locale.COMMON.ERROR_WITHOUT_CODE || '客户端发生错误'

  const props = {
    meta: {
      title: `${statusCode || '错误'} - ${siteConfig('TITLE')}`,
      description: errorMessage,
      type: 'website'
    },
    statusCode,
    errorMessage
  }

  return <DynamicLayout theme={siteConfig('THEME')} layoutName='LayoutError' {...props} />
}

Error.getInitialProps = ({ res, err }) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404
  
  // 记录服务端错误
  if (err) {
    console.error('服务端错误:', err)
  }
  
  return { statusCode, hasGetInitialPropsRun: true }
}

export default Error
