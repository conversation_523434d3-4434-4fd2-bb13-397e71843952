#!/usr/bin/env node

/**
 * Vercel专用构建脚本
 * 专门处理Vercel环境中的网络连接问题和构建优化
 */

const { spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 Vercel构建开始...')

// Vercel构建配置
const VERCEL_BUILD_CONFIG = {
  maxRetries: 5,
  retryDelay: 2000, // 2秒
  timeout: 900000, // 15分钟
  networkErrorPatterns: [
    'ECONNRESET',
    'ETIMEDOUT',
    'ENOTFOUND',
    'ECONNREFUSED',
    'read ECONNRESET',
    'RequestError',
    'FetchError',
    'socket hang up',
    'network timeout',
    'getaddrinfo ENOTFOUND'
  ]
}

/**
 * 设置Vercel环境变量
 */
function setupVercelEnv() {
  console.log('⚙️ 设置Vercel环境变量...')
  
  // 设置关键环境变量
  process.env.NEXT_TELEMETRY_DISABLED = '1'
  process.env.SKIP_ENV_VALIDATION = '1'
  process.env.NODE_ENV = 'production'
  process.env.VERCEL = '1'
  
  console.log('✅ 环境变量设置完成')
}

/**
 * 检查是否是网络错误
 * @param {string} output - 构建输出
 * @returns {boolean} 是否是网络错误
 */
function isNetworkError(output) {
  return VERCEL_BUILD_CONFIG.networkErrorPatterns.some(pattern => 
    output.toLowerCase().includes(pattern.toLowerCase())
  )
}

/**
 * 运行Next.js构建
 * @returns {Promise<{success: boolean, output: string}>}
 */
function runNextBuild() {
  return new Promise((resolve) => {
    console.log('📦 执行Next.js构建...')
    
    const buildProcess = spawn('npx', ['next', 'build'], {
      stdio: 'pipe',
      shell: true,
      cwd: process.cwd(),
      env: {
        ...process.env,
        FORCE_COLOR: '1' // 保持颜色输出
      }
    })

    let output = ''
    let errorOutput = ''

    buildProcess.stdout.on('data', (data) => {
      const text = data.toString()
      output += text
      process.stdout.write(text) // 实时显示输出
    })

    buildProcess.stderr.on('data', (data) => {
      const text = data.toString()
      errorOutput += text
      process.stderr.write(text) // 实时显示错误
    })

    // 设置超时
    const timeout = setTimeout(() => {
      console.log('⏰ 构建超时，终止进程...')
      buildProcess.kill('SIGTERM')
      resolve({
        success: false,
        output: output + errorOutput + '\n构建超时'
      })
    }, VERCEL_BUILD_CONFIG.timeout)

    buildProcess.on('close', (code) => {
      clearTimeout(timeout)
      const fullOutput = output + errorOutput
      
      resolve({
        success: code === 0,
        output: fullOutput
      })
    })

    buildProcess.on('error', (error) => {
      clearTimeout(timeout)
      console.error('构建进程错误:', error)
      resolve({
        success: false,
        output: output + errorOutput + `\n进程错误: ${error.message}`
      })
    })
  })
}

/**
 * 主构建函数
 */
async function main() {
  try {
    // 设置环境
    setupVercelEnv()
    
    let lastError = null
    let attempt = 1
    
    while (attempt <= VERCEL_BUILD_CONFIG.maxRetries) {
      console.log(`\n🔄 构建尝试 ${attempt}/${VERCEL_BUILD_CONFIG.maxRetries}`)
      
      const result = await runNextBuild()
      
      if (result.success) {
        console.log('✅ Vercel构建成功！')
        
        // 生成构建报告
        const report = {
          success: true,
          attempts: attempt,
          timestamp: new Date().toISOString(),
          platform: 'Vercel',
          message: 'Vercel构建成功完成'
        }
        
        try {
          fs.writeFileSync(
            path.join(process.cwd(), 'vercel-build-report.json'),
            JSON.stringify(report, null, 2)
          )
        } catch (e) {
          console.warn('无法写入构建报告:', e.message)
        }
        
        process.exit(0)
      }
      
      lastError = result.output
      
      // 检查是否是网络错误
      const isNetworkIssue = isNetworkError(result.output)
      
      console.log(`❌ 构建失败 (尝试 ${attempt}/${VERCEL_BUILD_CONFIG.maxRetries})`)
      
      if (isNetworkIssue) {
        console.log('🌐 检测到网络错误，准备重试...')
      } else {
        console.log('⚠️ 非网络错误，可能需要手动修复')
        
        // 非网络错误，提前退出
        if (attempt >= 2) {
          console.log('💡 连续非网络错误，停止重试')
          break
        }
      }
      
      // 如果不是最后一次尝试，等待后重试
      if (attempt < VERCEL_BUILD_CONFIG.maxRetries) {
        const delay = VERCEL_BUILD_CONFIG.retryDelay * Math.pow(1.5, attempt - 1)
        console.log(`⏳ 等待 ${delay / 1000} 秒后重试...`)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
      
      attempt++
    }
    
    // 所有尝试都失败了
    console.log('💥 Vercel构建失败')
    
    // 生成失败报告
    const report = {
      success: false,
      attempts: VERCEL_BUILD_CONFIG.maxRetries,
      timestamp: new Date().toISOString(),
      platform: 'Vercel',
      lastError: lastError,
      suggestions: [
        '检查网络连接稳定性',
        '验证Notion API配置',
        '检查环境变量设置',
        '查看Vercel构建日志',
        '尝试重新部署'
      ]
    }
    
    try {
      fs.writeFileSync(
        path.join(process.cwd(), 'vercel-build-report.json'),
        JSON.stringify(report, null, 2)
      )
    } catch (e) {
      console.warn('无法写入构建报告:', e.message)
    }
    
    console.log('📊 构建报告已保存')
    console.log('💡 建议检查网络连接和API配置')
    
    process.exit(1)
    
  } catch (error) {
    console.error('构建脚本执行失败:', error)
    process.exit(1)
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason)
  process.exit(1)
})

// 运行主函数
main()
