{"success": false, "mode": "safe-build", "timestamp": "2025-07-30T22:51:41.097Z", "platform": "Vercel", "lastError": "Deleted existing sitemap.xml from public directory\nDeleted existing sitemap.xml from root directory\n🔒 启用静态构建模式\n\n> Build error occurred\nError: Specified \"i18n\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-i18n\n    at assignDefaults \u001b[90m(C:\\Users\\<USER>\\Desktop\\wobshare\\\u001b[39mnode_modules\\\u001b[4mnext\u001b[24m\\dist\\server\\config.js:271:19\u001b[90m)\u001b[39m\n    at loadConfig \u001b[90m(C:\\Users\\<USER>\\Desktop\\wobshare\\\u001b[39mnode_modules\\\u001b[4mnext\u001b[24m\\dist\\server\\config.js:769:32\u001b[90m)\u001b[39m\n    at async Span.traceAsyncFn \u001b[90m(C:\\Users\\<USER>\\Desktop\\wobshare\\\u001b[39mnode_modules\\\u001b[4mnext\u001b[24m\\dist\\trace\\trace.js:154:20\u001b[90m)\u001b[39m\n    at async \u001b[90mC:\\Users\\<USER>\\Desktop\\wobshare\\\u001b[39mnode_modules\\\u001b[4mnext\u001b[24m\\dist\\build\\index.js:372:28\n    at async Span.traceAsyncFn \u001b[90m(C:\\Users\\<USER>\\Desktop\\wobshare\\\u001b[39mnode_modules\\\u001b[4mnext\u001b[24m\\dist\\trace\\trace.js:154:20\u001b[90m)\u001b[39m\n    at async build \u001b[90m(C:\\Users\\<USER>\\Desktop\\wobshare\\\u001b[39mnode_modules\\\u001b[4mnext\u001b[24m\\dist\\build\\index.js:366:9\u001b[90m)\u001b[39m\n", "suggestions": ["检查代码语法错误", "验证依赖包安装", "查看详细错误日志", "尝试本地构建测试"]}