import { NextResponse } from 'next/server'

export function middleware(request) {
  const response = NextResponse.next()

  // 检测部署平台
  const isVercel = process.env.VERCEL === '1'
  const isCloudflare = process.env.CF_PAGES === '1'
  const isNetlify = process.env.NETLIFY === 'true'

  // 设置安全头部
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')

  // 设置缓存头部
  if (request.nextUrl.pathname.startsWith('/api/')) {
    // API 路由缓存策略
    response.headers.set('Cache-Control', 'public, max-age=60, s-maxage=300')
  } else if (request.nextUrl.pathname.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg)$/)) {
    // 静态资源缓存策略
    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable')
  } else {
    // 页面缓存策略
    response.headers.set('Cache-Control', 'public, max-age=0, s-maxage=86400, stale-while-revalidate=86400')
  }

  // 平台特定优化
  if (isVercel) {
    // Vercel 特定头部
    response.headers.set('X-Powered-By', 'Vercel')
  } else if (isCloudflare) {
    // Cloudflare 特定头部
    response.headers.set('X-Powered-By', 'Cloudflare Pages')
  } else if (isNetlify) {
    // Netlify 特定头部
    response.headers.set('X-Powered-By', 'Netlify')
  }

  // 处理预检请求
  if (request.method === 'OPTIONS') {
    response.headers.set('Access-Control-Allow-Origin', '*')
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    return new Response(null, { status: 200, headers: response.headers })
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}
