import { siteConfig } from '@/lib/config'
import { loadResourceWithFallback } from '@/lib/utils/resourceLoader'
import { useEffect, useRef, useState } from 'react'

/**
 * 音乐播放器
 * @returns
 */
const Player = () => {
  const [player, setPlayer] = useState()
  const ref = useRef(null)
  const lrcType = JSON.parse(siteConfig('MUSIC_PLAYER_LRC_TYPE'))
  const playerVisible = JSON.parse(siteConfig('MUSIC_PLAYER_VISIBLE'))
  const autoPlay = JSON.parse(siteConfig('MUSIC_PLAYER_AUTO_PLAY'))
  const meting = JSON.parse(siteConfig('MUSIC_PLAYER_METING'))
  const order = siteConfig('MUSIC_PLAYER_ORDER')
  const audio = siteConfig('MUSIC_PLAYER_AUDIO_LIST')

  const musicPlayerEnable = siteConfig('MUSIC_PLAYER')
  const musicPlayerCDN = siteConfig('MUSIC_PLAYER_CDN_URL')
  const musicMetingEnable = siteConfig('MUSIC_PLAYER_METING')
  const musicMetingCDNUrl = siteConfig(
    'MUSIC_PLAYER_METING_CDN_URL',
    'https://cdnjs.cloudflare.com/ajax/libs/meting/2.0.1/Meting.min.js'
  )

  const initMusicPlayer = async () => {
    if (!musicPlayerEnable) {
      return
    }

    try {
      // 首先加载CSS
      try {
        await loadResourceWithFallback('aplayerCSS', 'css', 10000)
      } catch (error) {
        // 静默处理CSS加载失败
      }

      // 然后加载JS
      await loadResourceWithFallback('aplayer', 'js', 12000)

      if (musicMetingEnable) {
        try {
          await loadResourceWithFallback('meting', 'js', 10000)
        } catch (error) {
          // 静默处理MetingJS加载失败
        }
      }

      // 确保容器存在且APlayer已加载
      if (!meting && window.APlayer && ref.current) {
        // 添加延迟确保DOM完全准备好
        setTimeout(() => {
          try {
            setPlayer(
              new window.APlayer({
                container: ref.current,
                fixed: true,
                lrcType: lrcType,
                autoplay: autoPlay,
                order: order,
                audio: audio
              })
            )
          } catch (error) {
            // 静默处理初始化失败
          }
        }, 100)
      }
    } catch (error) {
      // 静默处理整体初始化失败
    }
  }

  useEffect(() => {
    if (musicPlayerEnable) {
      initMusicPlayer()
    }

    return () => {
      setPlayer(undefined)
    }
  }, [])

  // 加载CSS样式
  useEffect(() => {
    if (musicPlayerEnable) {
      loadResourceWithFallback('aplayerCSS', 'css', 15000).catch(error => {
        console.warn('APlayer CSS加载失败:', error.message)
      })
    }
  }, [musicPlayerEnable])

  return (
    <div className={playerVisible ? 'visible' : 'invisible'}>
      {meting ? (
        <meting-js
          fixed='true'
          type='playlist'
          preload='auto'
          api={siteConfig(
            'MUSIC_PLAYER_METING_API',
            'https://api.i-meto.com/meting/api?server=:server&type=:type&id=:id&r=:r'
          )}
          autoplay={autoPlay}
          order={siteConfig('MUSIC_PLAYER_ORDER')}
          server={siteConfig('MUSIC_PLAYER_METING_SERVER')}
          id={siteConfig('MUSIC_PLAYER_METING_ID')}
        />
      ) : (
        <div ref={ref} data-player={player} />
      )}
    </div>
  )
}

export default Player
