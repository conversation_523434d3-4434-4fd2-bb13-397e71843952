#!/usr/bin/env node

/**
 * 简单的构建脚本
 * 设置环境变量并运行Next.js构建
 */

const { spawn } = require('child_process')

// 设置环境变量
process.env.SKIP_NETWORK_REQUESTS = 'true'
process.env.BUILD_OFFLINE = 'true'
process.env.NEXT_TELEMETRY_DISABLED = '1'

console.log('🚀 开始构建...')

// 运行Next.js构建
const buildProcess = spawn('npx', ['next', 'build'], {
  stdio: 'inherit',
  shell: true,
  env: process.env
})

buildProcess.on('close', (code) => {
  if (code === 0) {
    console.log('✅ 构建成功！')
  } else {
    console.log('❌ 构建失败')
    process.exit(code)
  }
})

buildProcess.on('error', (error) => {
  console.error('构建错误:', error)
  process.exit(1)
})
