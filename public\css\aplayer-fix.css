/**
 * APlayer 样式修复
 * 修复控制按钮位置和可见性问题
 */

/* 主容器修复 */
.aplayer {
  position: relative !important;
  min-height: 120px !important;
  overflow: visible !important;
}

/* 控制器容器修复 */
.aplayer .aplayer-controller {
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  height: 40px !important;
  padding: 8px 0 !important;
  margin-top: 12px !important;
  z-index: 10 !important;
}

/* 进度条区域 */
.aplayer .aplayer-controller .aplayer-bar-wrap {
  flex: 1 !important;
  margin: 0 12px !important;
  height: 4px !important;
  position: relative !important;
}

/* 右侧控制按钮组 */
.aplayer .aplayer-controller .aplayer-volume-wrap,
.aplayer .aplayer-controller .aplayer-mode,
.aplayer .aplayer-controller .aplayer-menu {
  position: relative !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 32px !important;
  height: 32px !important;
  margin: 0 2px !important;
  cursor: pointer !important;
  border-radius: 4px !important;
  background: transparent !important;
  transition: background 0.2s ease !important;
  z-index: 11 !important;
}

.aplayer .aplayer-controller .aplayer-volume-wrap:hover,
.aplayer .aplayer-controller .aplayer-mode:hover,
.aplayer .aplayer-controller .aplayer-menu:hover {
  background: rgba(0,0,0,0.05) !important;
}

/* 图标修复 */
.aplayer .aplayer-controller .aplayer-volume-wrap .aplayer-icon,
.aplayer .aplayer-controller .aplayer-mode .aplayer-icon,
.aplayer .aplayer-controller .aplayer-menu .aplayer-icon {
  width: 16px !important;
  height: 16px !important;
  fill: #666 !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 12 !important;
  pointer-events: none !important;
}

/* 音量控制面板位置修复 */
.aplayer .aplayer-controller .aplayer-volume-wrap .aplayer-volume-bar-wrap {
  position: absolute !important;
  bottom: 100% !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 4px !important;
  height: 60px !important;
  background: rgba(255,255,255,0.9) !important;
  border: 1px solid rgba(0,0,0,0.1) !important;
  border-radius: 2px !important;
  margin-bottom: 8px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
  z-index: 100 !important;
}

.aplayer .aplayer-controller .aplayer-volume-wrap .aplayer-volume-bar-wrap .aplayer-volume-bar {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  background: #667eea !important;
  border-radius: 2px !important;
  transition: height 0.2s ease !important;
}

/* 菜单面板位置修复 */
.aplayer .aplayer-controller .aplayer-menu .aplayer-menu-panel {
  position: absolute !important;
  bottom: 100% !important;
  right: 0 !important;
  background: white !important;
  border: 1px solid rgba(0,0,0,0.1) !important;
  border-radius: 4px !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
  margin-bottom: 8px !important;
  min-width: 120px !important;
  z-index: 100 !important;
}

/* 播放模式按钮修复 */
.aplayer .aplayer-controller .aplayer-mode .aplayer-icon {
  transition: transform 0.2s ease !important;
}

.aplayer .aplayer-controller .aplayer-mode:active .aplayer-icon {
  transform: scale(0.9) !important;
}

/* 时间显示修复 */
.aplayer .aplayer-controller .aplayer-time {
  font-size: 12px !important;
  color: #666 !important;
  min-width: 40px !important;
  text-align: center !important;
  line-height: 1 !important;
  margin: 0 4px !important;
}

/* 播放按钮修复 */
.aplayer .aplayer-controller .aplayer-play {
  width: 36px !important;
  height: 36px !important;
  border-radius: 50% !important;
  background: #667eea !important;
  border: none !important;
  color: white !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  margin-right: 12px !important;
}

.aplayer .aplayer-controller .aplayer-play:hover {
  background: #5a67d8 !important;
  transform: scale(1.05) !important;
}

.aplayer .aplayer-controller .aplayer-play .aplayer-icon {
  width: 14px !important;
  height: 14px !important;
  fill: white !important;
}

/* 响应式修复 */
@media (max-width: 768px) {
  .aplayer .aplayer-controller {
    height: 36px !important;
    padding: 6px 0 !important;
  }
  
  .aplayer .aplayer-controller .aplayer-volume-wrap,
  .aplayer .aplayer-controller .aplayer-mode,
  .aplayer .aplayer-controller .aplayer-menu {
    width: 28px !important;
    height: 28px !important;
  }
  
  .aplayer .aplayer-controller .aplayer-play {
    width: 32px !important;
    height: 32px !important;
  }
  
  .aplayer .aplayer-controller .aplayer-volume-wrap .aplayer-volume-bar-wrap {
    height: 50px !important;
  }
}

/* 确保在所有主题下都可见 */
.dark .aplayer .aplayer-controller .aplayer-volume-wrap .aplayer-icon,
.dark .aplayer .aplayer-controller .aplayer-mode .aplayer-icon,
.dark .aplayer .aplayer-controller .aplayer-menu .aplayer-icon {
  fill: #ccc !important;
}

.dark .aplayer .aplayer-controller .aplayer-volume-wrap:hover,
.dark .aplayer .aplayer-controller .aplayer-mode:hover,
.dark .aplayer .aplayer-controller .aplayer-menu:hover {
  background: rgba(255,255,255,0.1) !important;
}

/* 修复层级问题 */
.aplayer * {
  box-sizing: border-box !important;
}

.aplayer .aplayer-controller * {
  position: relative !important;
  z-index: inherit !important;
}
