/**
 * 应用启动脚本
 * 按优先级顺序加载所有资源
 */

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
  // 资源加载状态
  const loadingState = {
    'desktop-video': false,
    'live2d': false,
    'aplayer': false,
    'chatbase': false,
    'others': false
  }

  // 1. 优先级1：桌面背景视频
  function loadDesktopVideo() {
    return new Promise((resolve) => {
      const video = document.createElement('video')
      video.src = '/favtion.mp4'
      video.preload = 'auto'
      video.muted = true
      video.loop = true
      video.style.display = 'none'

      document.body.appendChild(video)

      const onLoad = () => {
        loadingState['desktop-video'] = true
        resolve()
      }

      const onError = () => {
        loadingState['desktop-video'] = true
        resolve()
      }

      video.addEventListener('loadeddata', onLoad)
      video.addEventListener('error', onError)

      // 5秒超时
      setTimeout(() => {
        if (!loadingState['desktop-video']) {
          onLoad()
        }
      }, 5000)
    })
  }
  
  // 2. 优先级2：Live2D
  function loadLive2D() {
    return new Promise((resolve) => {
      // 触发Live2D组件加载
      const event = new CustomEvent('loadLive2D')
      window.dispatchEvent(event)

      // 等待一段时间让Live2D加载
      setTimeout(() => {
        loadingState['live2d'] = true
        resolve()
      }, 2000)
    })
  }

  // 3. 优先级3：APlayer
  function loadAPlayer() {
    return new Promise((resolve) => {
      // 触发APlayer组件加载
      const event = new CustomEvent('loadAPlayer')
      window.dispatchEvent(event)

      // 等待一段时间让APlayer加载
      setTimeout(() => {
        loadingState['aplayer'] = true
        resolve()
      }, 3000)
    })
  }

  // 4. 优先级4：Chatbase
  function loadChatbase() {
    return new Promise((resolve) => {
      // 查找Chatbase容器
      const chatbaseContainer = document.querySelector('[data-chatbase]') ||
                               document.querySelector('.chatbase-container') ||
                               document.getElementById('chatbase')

      if (chatbaseContainer) {
        // 触发Chatbase加载
        const event = new CustomEvent('loadChatbase')
        window.dispatchEvent(event)
      }

      setTimeout(() => {
        loadingState['chatbase'] = true
        resolve()
      }, 1000)
    })
  }

  // 5. 优先级5：其他资源
  function loadOthers() {
    return new Promise((resolve) => {
      // 触发其他组件加载
      const event = new CustomEvent('loadOthers')
      window.dispatchEvent(event)

      setTimeout(() => {
        loadingState['others'] = true
        resolve()
      }, 2000)
    })
  }
  
  // 按顺序执行加载
  async function loadAllResources() {
    try {
      await loadDesktopVideo()
      await new Promise(resolve => setTimeout(resolve, 200)) // 短暂延迟
      
      await loadLive2D()
      await new Promise(resolve => setTimeout(resolve, 200))
      
      await loadAPlayer()
      await new Promise(resolve => setTimeout(resolve, 200))
      
      await loadChatbase()
      await new Promise(resolve => setTimeout(resolve, 200))
      
      await loadOthers()
      
      // 触发全部加载完成事件
      const event = new CustomEvent('allResourcesLoaded', {
        detail: loadingState
      })
      window.dispatchEvent(event)

    } catch (error) {
      // 静默处理错误
    }
  }
  
  // 开始加载
  loadAllResources()
  
  // 提供全局方法检查加载状态
  window.getResourceLoadingState = function() {
    return { ...loadingState }
  }
  
  window.isResourceLoaded = function(resourceName) {
    return loadingState[resourceName] || false
  }
})

// 页面可见性变化时的处理
document.addEventListener('visibilitychange', function() {
  if (document.visibilityState === 'visible') {
    // 检查关键资源是否正常
    setTimeout(() => {
      if (typeof window.APlayer === 'undefined' && window.APlayerFallback) {
        window.APlayer = window.APlayerFallback
      }

      if (typeof window.loadlive2d === 'undefined' && window.Live2DFallback) {
        window.Live2DFallback.init()
      }
    }, 1000)
  }
})
