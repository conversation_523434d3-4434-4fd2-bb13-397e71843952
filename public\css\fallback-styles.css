/**
 * 备用样式文件
 * 当CDN样式加载失败时使用
 */

/* APlayer 样式修复 */
.aplayer {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin: 16px 0;
  padding: 16px;
  border: 1px solid #e1e5e9;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
  min-height: 120px; /* 确保有足够高度 */
}

/* 修复控制按钮位置 */
.aplayer .aplayer-controller {
  position: relative;
  display: flex;
  align-items: center;
  height: 40px;
  margin-top: 12px;
  padding: 8px 0;
}

.aplayer .aplayer-controller .aplayer-bar-wrap {
  flex: 1;
  margin: 0 12px;
  height: 4px;
  position: relative;
}

.aplayer .aplayer-controller .aplayer-volume-wrap,
.aplayer .aplayer-controller .aplayer-mode,
.aplayer .aplayer-controller .aplayer-menu {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin: 0 4px;
  cursor: pointer;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.aplayer .aplayer-controller .aplayer-volume-wrap:hover,
.aplayer .aplayer-controller .aplayer-mode:hover,
.aplayer .aplayer-controller .aplayer-menu:hover {
  background: rgba(0,0,0,0.05);
}

/* 确保图标可见 */
.aplayer .aplayer-controller .aplayer-volume-wrap .aplayer-icon,
.aplayer .aplayer-controller .aplayer-mode .aplayer-icon,
.aplayer .aplayer-controller .aplayer-menu .aplayer-icon {
  width: 16px;
  height: 16px;
  fill: #666;
  opacity: 1;
  position: relative;
  z-index: 1;
}

/* 音量控制面板 */
.aplayer .aplayer-controller .aplayer-volume-wrap .aplayer-volume-bar-wrap {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 60px;
  background: rgba(0,0,0,0.1);
  border-radius: 2px;
  margin-bottom: 8px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.aplayer .aplayer-controller .aplayer-volume-wrap:hover .aplayer-volume-bar-wrap {
  opacity: 1;
  visibility: visible;
}

.aplayer-info {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.aplayer-pic {
  width: 48px;
  height: 48px;
  border-radius: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.aplayer-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.aplayer-author {
  font-size: 12px;
  color: #666;
}

.aplayer-controller {
  display: flex;
  align-items: center;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.aplayer-bar-wrap {
  flex: 1;
  margin: 0 12px;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  cursor: pointer;
}

.aplayer-bar {
  height: 100%;
  background: #667eea;
  border-radius: 2px;
  width: 0%;
  transition: width 0.3s ease;
}

.aplayer-time {
  font-size: 12px;
  color: #666;
  min-width: 40px;
}

.aplayer-button {
  width: 32px;
  height: 32px;
  border: none;
  background: #667eea;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: background 0.3s ease;
}

.aplayer-button:hover {
  background: #5a67d8;
}

/* Live2D 备用样式 */
#live2d-widget {
  position: fixed;
  bottom: 20px;
  left: 20px;
  z-index: 999;
}

.live2d-fallback {
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  cursor: pointer;
  transition: transform 0.3s ease;
}

.live2d-fallback:hover {
  transform: scale(1.05);
}

/* Twikoo 评论系统备用样式 */
.twikoo-fallback {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  margin: 20px 0;
}

.twikoo-fallback-title {
  color: #6c757d;
  font-size: 16px;
  margin-bottom: 12px;
}

.twikoo-fallback-desc {
  color: #adb5bd;
  font-size: 14px;
}

/* Prism 代码高亮备用样式 */
.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #708090;
}

.token.punctuation {
  color: #999;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol,
.token.deleted {
  color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
  color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
  color: #9a6e3a;
}

.token.atrule,
.token.attr-value,
.token.keyword {
  color: #07a;
}

.token.function,
.token.class-name {
  color: #dd4a68;
}

.token.regex,
.token.important,
.token.variable {
  color: #e90;
}

/* 加载状态样式 */
.resource-loading {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 14px;
  color: #6c757d;
}

.resource-loading::before {
  content: "⏳";
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 错误状态样式 */
.resource-error {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 4px;
  font-size: 14px;
  color: #c53030;
}

.resource-error::before {
  content: "⚠️";
}

/* 响应式设计 */
@media (max-width: 768px) {
  .aplayer {
    margin: 8px;
    padding: 12px;
  }
  
  #live2d-widget {
    bottom: 10px;
    left: 10px;
  }
  
  .live2d-fallback {
    width: 120px;
    height: 120px;
    font-size: 12px;
  }
  
  .twikoo-fallback {
    margin: 10px;
    padding: 16px;
  }
}
