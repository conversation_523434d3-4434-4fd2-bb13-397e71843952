import { useEffect } from 'react'

const GlobalErrorHandler = () => {
  useEffect(() => {
    // 捕获未处理的Promise拒绝
    const handleUnhandledRejection = (event) => {
      console.warn('Unhandled promise rejection:', event.reason)
      
      // 如果是资源加载错误，尝试静默处理
      if (event.reason && typeof event.reason === 'string') {
        if (event.reason.includes('classList') || 
            event.reason.includes('live2d') || 
            event.reason.includes('lrc.js')) {
          event.preventDefault() // 阻止错误显示在控制台
        }
      }
    }

    // 捕获全局JavaScript错误
    const handleError = (event) => {
      const { error, filename, lineno, colno, message } = event
      
      // 过滤掉已知的第三方库错误
      const knownErrors = [
        'live2d.min.js',
        'lrc.js',
        'Cannot read properties of undefined',
        'classList',
        'busuanzi',
        'chatbase'
      ]
      
      const shouldIgnore = knownErrors.some(pattern => 
        filename?.includes(pattern) || message?.includes(pattern)
      )
      
      if (shouldIgnore) {
        console.warn('忽略已知的第三方库错误:', { message, filename, lineno, colno })
        event.preventDefault()
        return true
      }
      
      console.error('Global error:', { error, filename, lineno, colno, message })
    }

    // 添加事件监听器
    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    window.addEventListener('error', handleError)

    // 清理函数
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
      window.removeEventListener('error', handleError)
    }
  }, [])

  return null // 这个组件不渲染任何内容
}

export default GlobalErrorHandler
