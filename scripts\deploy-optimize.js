#!/usr/bin/env node

/**
 * 部署优化脚本
 * 在构建前执行各种优化操作
 */

const fs = require('fs')
const path = require('path')

console.log('🚀 开始部署优化...')

// 1. 清理临时文件
function cleanTempFiles() {
  console.log('🧹 清理临时文件...')
  
  const tempFiles = [
    'q.txt',
    '.next',
    'out',
    'node_modules/.cache'
  ]
  
  tempFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file)
    if (fs.existsSync(filePath)) {
      try {
        if (fs.statSync(filePath).isDirectory()) {
          fs.rmSync(filePath, { recursive: true, force: true })
        } else {
          fs.unlinkSync(filePath)
        }
        console.log(`✅ 已删除: ${file}`)
      } catch (error) {
        console.warn(`⚠️ 删除失败: ${file}`, error.message)
      }
    }
  })
}

// 2. 优化package.json
function optimizePackageJson() {
  console.log('📦 优化 package.json...')
  
  const packagePath = path.join(process.cwd(), 'package.json')
  if (fs.existsSync(packagePath)) {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
    
    // 注意：不要在Vercel部署时删除开发依赖，因为某些包在构建时需要
    // 只在本地优化时才删除不必要的开发依赖
    if (process.env.NODE_ENV === 'production' && !process.env.VERCEL) {
      const unnecessaryDevDeps = [
        // 暂时不删除任何依赖，避免构建问题
      ]

      unnecessaryDevDeps.forEach(dep => {
        if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
          console.log(`🗑️ 移除开发依赖: ${dep}`)
          delete packageJson.devDependencies[dep]
        }
      })
    }
    
    // 优化脚本
    if (!packageJson.scripts['build:optimized']) {
      packageJson.scripts['build:optimized'] = 'node scripts/deploy-optimize.js && next build'
    }
    
    fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2))
    console.log('✅ package.json 优化完成')
  }
}

// 3. 创建优化的环境变量文件
function createOptimizedEnv() {
  console.log('🔧 创建优化的环境变量...')
  
  const envContent = `# 部署优化环境变量
NEXT_PUBLIC_ENABLE_PRELOAD=true
NEXT_PUBLIC_ENABLE_COMPRESSION=true
NEXT_PUBLIC_CDN_OPTIMIZATION=true
NEXT_PUBLIC_ENABLE_GLOBAL_ERROR_HANDLER=true
NEXT_PUBLIC_SILENCE_THIRD_PARTY_ERRORS=true
NEXT_PUBLIC_ENABLE_LAZY_LOADING=true
NEXT_PUBLIC_ENABLE_CODE_SPLITTING=true
NEXT_PUBLIC_MOBILE_OPTIMIZATION=true
NEXT_PUBLIC_SECURITY_HEADERS=true
`
  
  const envPath = path.join(process.cwd(), '.env.production')
  fs.writeFileSync(envPath, envContent)
  console.log('✅ 生产环境变量文件已创建')
}

// 4. 检查关键文件
function checkCriticalFiles() {
  console.log('🔍 检查关键文件...')
  
  const criticalFiles = [
    'next.config.js',
    'blog.config.js',
    'components/Live2D.js',
    'components/Player.js',
    'lib/utils/resourceLoader.js'
  ]
  
  criticalFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file)
    if (!fs.existsSync(filePath)) {
      console.error(`❌ 关键文件缺失: ${file}`)
      process.exit(1)
    } else {
      console.log(`✅ 文件存在: ${file}`)
    }
  })
}

// 5. 生成部署报告
function generateDeployReport() {
  console.log('📊 生成部署报告...')
  
  const report = {
    timestamp: new Date().toISOString(),
    platform: process.env.VERCEL ? 'Vercel' : 
              process.env.CF_PAGES ? 'Cloudflare Pages' :
              process.env.NETLIFY ? 'Netlify' : 'Unknown',
    nodeVersion: process.version,
    optimizations: [
      '✅ 资源预加载优化',
      '✅ CDN故障转移',
      '✅ 全局错误处理',
      '✅ 代码分割优化',
      '✅ 图片域名配置',
      '✅ 安全头部设置'
    ]
  }
  
  const reportPath = path.join(process.cwd(), 'deploy-report.json')
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
  console.log('✅ 部署报告已生成')
}

// 主函数
function main() {
  try {
    cleanTempFiles()
    optimizePackageJson()
    createOptimizedEnv()
    checkCriticalFiles()
    generateDeployReport()
    
    console.log('🎉 部署优化完成！')
    console.log('💡 提示：现在可以运行 npm run build 进行构建')
  } catch (error) {
    console.error('❌ 部署优化失败:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = { main }
